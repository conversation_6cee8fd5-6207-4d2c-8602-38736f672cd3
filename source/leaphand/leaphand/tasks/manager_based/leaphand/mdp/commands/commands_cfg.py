# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from dataclasses import MISSING

from isaaclab.managers import CommandTermCfg
from isaaclab.utils import configclass

from .rotation_axis_command import RotationAxisCommand


@configclass
class RotationAxisCommandCfg(CommandTermCfg):
    """Configuration for the rotation axis command term.

    Please refer to the :class:`RotationAxisCommand` class for more details.
    """

    class_type: type = RotationAxisCommand
    resampling_time_range: tuple[float, float] = (1e6, 1e6)  # no resampling based on time by default

    rotation_axis_mode: str = "z_axis"
    """Rotation axis mode. Options: 'z_axis', 'x_axis', 'y_axis', 'random', 'mixed'."""

    change_rotation_axis_interval: int = 0
    """Interval (in steps) to change rotation axis. 0 means no change."""

    rotation_axis_noise: float = 0.0
    """Noise to add to the rotation axis. Range: [0.0, 1.0]."""

    debug_vis: bool = False
    """Whether to visualize the rotation axis command."""
