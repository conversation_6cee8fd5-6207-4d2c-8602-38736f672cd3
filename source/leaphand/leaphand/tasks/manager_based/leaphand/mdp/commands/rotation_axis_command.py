# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Sub-module containing command generators for rotation axis goals."""

from __future__ import annotations

import torch
from collections.abc import Sequence
from typing import TYPE_CHECKING

from isaaclab.managers import CommandTerm

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv

    from .commands_cfg import RotationAxisCommandCfg


class RotationAxisCommand(CommandTerm):
    """Command term that generates rotation axis commands for continuous rotation tasks.

    This command term generates 3D rotation axis vectors for goal-conditioned rotation tasks.
    The rotation axis follows the right-hand rule convention where the thumb points in the 
    positive axis direction and the fingers curl in the positive rotation direction.

    The command supports different rotation axis modes:
    - "z_axis": Fixed Z-axis rotation
    - "x_axis": Fixed X-axis rotation  
    - "y_axis": Fixed Y-axis rotation
    - "random": Random axis selection
    - "mixed": Mixed mode with curriculum learning

    Unlike typical command terms that resample based on time, this command term can be
    configured to resample based on curriculum progression or fixed intervals.
    """

    cfg: RotationAxisCommandCfg
    """Configuration for the command term."""

    def __init__(self, cfg: RotationAxisCommandCfg, env: ManagerBasedRLEnv):
        """Initialize the command term class.

        Args:
            cfg: The configuration parameters for the command term.
            env: The environment object.
        """
        # initialize the base class
        super().__init__(cfg, env)

        # create buffers to store the command
        # -- rotation axis: (x, y, z) unit vector
        self.rotation_axis_command = torch.zeros(self.num_envs, 3, device=self.device)
        

        
        # axis change interval tracking
        self.axis_change_counter = torch.zeros(self.num_envs, device=self.device, dtype=torch.long)
        
        # metrics for logging
        self.metrics["rotation_axis_x"] = torch.zeros(self.num_envs, device=self.device)
        self.metrics["rotation_axis_y"] = torch.zeros(self.num_envs, device=self.device) 
        self.metrics["rotation_axis_z"] = torch.zeros(self.num_envs, device=self.device)

        # initialize rotation axis
        self._resample_command(torch.arange(self.num_envs, device=self.device))

    def __str__(self) -> str:
        msg = "RotationAxisCommand:\n"
        msg += f"\tCommand dimension: {tuple(self.command.shape[1:])}\n"
        msg += f"\tRotation axis mode: {self.cfg.rotation_axis_mode}\n"
        msg += f"\tResampling time range: {self.cfg.resampling_time_range}"
        return msg

    """
    Properties
    """

    @property
    def command(self) -> torch.Tensor:
        """The desired rotation axis as a unit vector. Shape is (num_envs, 3)."""
        return self.rotation_axis_command

    """
    Operations.
    """

    def _update_metrics(self):
        """Update the metrics based on the current state."""
        # Store current rotation axis components for logging
        self.metrics["rotation_axis_x"][:] = self.rotation_axis_command[:, 0]
        self.metrics["rotation_axis_y"][:] = self.rotation_axis_command[:, 1]
        self.metrics["rotation_axis_z"][:] = self.rotation_axis_command[:, 2]

    def _resample_command(self, env_ids: Sequence[int]):
        """Resample the rotation axis command for specified environments.
        
        Args:
            env_ids: Environment IDs to resample commands for.
        """
        if len(env_ids) == 0:
            return
            
        # Get current rotation axis mode (considering curriculum)
        current_mode = self._get_current_axis_mode()
        
        # Generate rotation axis based on mode
        if current_mode == "z_axis":
            self.rotation_axis_command[env_ids] = torch.tensor([0.0, 0.0, 1.0], device=self.device)
        elif current_mode == "x_axis":
            self.rotation_axis_command[env_ids] = torch.tensor([1.0, 0.0, 0.0], device=self.device)
        elif current_mode == "y_axis":
            self.rotation_axis_command[env_ids] = torch.tensor([0.0, 1.0, 0.0], device=self.device)
        elif current_mode == "random":
            self._sample_random_axis(env_ids)
        elif current_mode == "mixed":
            self._sample_mixed_axis(env_ids)
        else:
            # Default to z_axis
            self.rotation_axis_command[env_ids] = torch.tensor([0.0, 0.0, 1.0], device=self.device)
            
        # Add noise if specified
        if self.cfg.rotation_axis_noise > 0.0:
            self._add_axis_noise(env_ids)
            
        # Reset axis change counter
        self.axis_change_counter[env_ids] = 0

    def _update_command(self):
        """Update the command based on the current state."""
        # Handle axis change interval
        if self.cfg.change_rotation_axis_interval > 0:
            self.axis_change_counter += 1
            change_mask = self.axis_change_counter >= self.cfg.change_rotation_axis_interval
            change_env_ids = change_mask.nonzero(as_tuple=False).squeeze(-1)
            if len(change_env_ids) > 0:
                self._resample_command(change_env_ids)

    def _get_current_axis_mode(self) -> str:
        """Get the current rotation axis mode."""
        return self.cfg.rotation_axis_mode

    def _sample_random_axis(self, env_ids: Sequence[int]):
        """Sample random rotation axes for specified environments."""
        # Sample random unit vectors on sphere
        random_vecs = torch.randn(len(env_ids), 3, device=self.device)
        random_vecs = random_vecs / torch.norm(random_vecs, dim=-1, keepdim=True)
        self.rotation_axis_command[env_ids] = random_vecs

    def _sample_mixed_axis(self, env_ids: Sequence[int]):
        """Sample mixed rotation axes (combination of fixed axes and random)."""
        # 50% chance for each of the three main axes, 50% for random
        choices = torch.randint(0, 4, (len(env_ids),), device=self.device)
        
        for i, env_id in enumerate(env_ids):
            choice = choices[i].item()
            if choice == 0:
                self.rotation_axis_command[env_id] = torch.tensor([1.0, 0.0, 0.0], device=self.device)
            elif choice == 1:
                self.rotation_axis_command[env_id] = torch.tensor([0.0, 1.0, 0.0], device=self.device)
            elif choice == 2:
                self.rotation_axis_command[env_id] = torch.tensor([0.0, 0.0, 1.0], device=self.device)
            else:
                # Random axis
                random_vec = torch.randn(3, device=self.device)
                random_vec = random_vec / torch.norm(random_vec)
                self.rotation_axis_command[env_id] = random_vec

    def _add_axis_noise(self, env_ids: Sequence[int]):
        """Add noise to rotation axis and renormalize."""
        noise = torch.randn(len(env_ids), 3, device=self.device) * self.cfg.rotation_axis_noise
        self.rotation_axis_command[env_ids] += noise
        # Renormalize to unit vectors
        norms = torch.norm(self.rotation_axis_command[env_ids], dim=-1, keepdim=True)
        self.rotation_axis_command[env_ids] = self.rotation_axis_command[env_ids] / norms



    def _set_debug_vis_impl(self, debug_vis: bool):
        """Set debug visualization for rotation axis commands."""
        raise NotImplementedError(f"Debug visualization is not implemented for {self.__class__.__name__}.")
