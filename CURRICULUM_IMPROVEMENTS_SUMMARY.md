# LeapHand课程学习系统改进总结

## 🎯 问题分析与解决方案

### 1. EventCfg与ADR课程学习的关系问题

**问题分析：**
- 当前`object_scale_mass`配置中`mass_distribution_params": (1.0, 1.0)`相当于没有随机化
- ADR课程学习需要正确的地址来修改EventCfg中的参数
- 需要理解Isaac Lab中EventManager和CurriculumManager的交互机制

**解决方案：**
```python
# 修改前（无效的随机化）
object_scale_mass = EventTerm(
    params={
        "mass_distribution_params": (1.0, 1.0),  # 始终乘以1.0，无随机化
        "operation": "scale",
    }
)

# 修改后（正确的ADR地址）
object_mass_adr = CurrTerm(
    func=mdp.modify_env_param,
    params={
        "address": "events.object_scale_mass.params.mass_distribution_params",  # 正确地址
        "modify_fn": leaphand_mdp.object_mass_adr,
        "modify_params": {
            "enable_step": 600_000,
            "max_strength_step": 1_200_000,
            "max_variation": 0.5  # 从(1.0,1.0)逐步变为(0.5,1.5)
        }
    }
)
```

### 2. ADR课程学习函数的实现机制

**修改的ADR函数：**
```python
def object_mass_adr(
    env: ManagerBasedRLEnv,
    env_ids: Sequence[int],
    old_value: tuple[float, float],  # 接收(min_scale, max_scale)
    enable_step: int = 600_000,
    max_strength_step: int = 1_200_000,
    max_variation: float = 0.5
) -> tuple[float, float]:  # 返回新的(min_scale, max_scale)
    """
    物体质量自适应域随机化 - 修改EventCfg中的mass_distribution_params
    """
    current_step = env.common_step_counter
    
    if current_step < enable_step:
        return mdp.modify_env_param.NO_CHANGE
    
    # 计算当前强度
    if current_step >= max_strength_step:
        strength = max_variation
    else:
        progress = (current_step - enable_step) / (max_strength_step - enable_step)
        strength = progress * max_variation
    
    # 计算新的随机化范围：1.0 ± strength
    min_scale = 1.0 - strength
    max_scale = 1.0 + strength
    
    return (min_scale, max_scale)
```

**工作流程：**
1. **0-60万步**: `mass_distribution_params`保持`(1.0, 1.0)`，无随机化
2. **60-120万步**: 逐步从`(1.0, 1.0)`变为`(0.5, 1.5)`，随机化强度递增
3. **120万步后**: 保持`(0.5, 1.5)`，物体质量在原始值的50%-150%范围内随机

### 3. 移除不需要的重力随机化

**移除的内容：**
- `gravity_adr`函数
- 所有课程学习配置中的`gravity_adr`项
- 验证脚本中的相关检查

**原因：** 手部操作任务通常不需要重力随机化，专注于质量和摩擦系数的随机化更有效。

### 4. 添加pose_diff_penalty奖励项

**新增的奖励函数：**
```python
def pose_diff_penalty(
    env: ManagerBasedRLEnv,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    natural_pose: dict[str, float] | None = None
) -> torch.Tensor:
    """计算手部姿态偏差惩罚 - 鼓励保持接近人手的自然姿态"""
    
    # 定义LeapHand的自然姿态（基于人手的自然弯曲）
    if natural_pose is None:
        natural_pose = {
            # 拇指关节 - 稍微内收和弯曲
            "a_thumb_j1": 0.2,   # 拇指根部内收
            "a_thumb_j2": 0.3,   # 拇指中间关节弯曲
            "a_thumb_j3": 0.2,   # 拇指末端关节弯曲
            "a_thumb_j4": 0.1,   # 拇指侧摆
            
            # 其他手指关节 - 自然弯曲
            # ... (详细配置见代码)
        }
    
    # 计算当前关节位置与自然姿态的L2平方差
    pose_diff_penalty = torch.sum((current_joint_pos - natural_joint_pos) ** 2, dim=-1)
    
    return pose_diff_penalty
```

**集成到奖励系统：**
```python
# 在RewardsCfg中添加
pose_diff_penalty = RewTerm(
    func=leaphand_mdp.pose_diff_penalty,
    weight=-0.01,  # 初始权重较轻
    params={"asset_cfg": SceneEntityCfg("robot")},
)

# 在课程学习中动态调整权重
pose_diff_penalty_weight = CurrTerm(
    func=mdp.modify_reward_weight,
    params={
        "term_name": "pose_diff_penalty",
        "weight": -0.02,  # 后期加重姿态约束
        "num_steps": 800_000
    }
)
```

## 📊 完整的课程学习时间表

### 奖励权重调整时间表
| 奖励项 | 0-30万步 | 30-50万步 | 50-80万步 | 80-100万步 | 100万步后 |
|--------|----------|-----------|-----------|------------|-----------|
| **抓取稳定性** | 2.0 | 2.0 | 1.5 | 1.5 | 1.0 |
| **旋转速度** | 10.0 | 15.0 | 15.0 | 20.0 | 20.0 |
| **掉落惩罚** | -50.0 | -50.0 | -50.0 | -100.0 | -150.0 |
| **姿态偏差** | -0.01 | -0.01 | -0.01 | -0.02 | -0.02 |

### 域随机化启用时间表
| 参数 | 0-60万步 | 60-120万步 | 120万步后 |
|------|----------|------------|-----------|
| **物体质量** | 无随机化 | (1.0,1.0)→(0.5,1.5) | (0.5,1.5) |
| **摩擦系数** | 无随机化 | (1.0,1.0)→(0.7,1.3) | (0.7,1.3) |

### 旋转轴复杂度时间表
| 阶段 | 0-40万步 | 40-80万步 | 80-120万步 | 120万步后 |
|------|----------|-----------|------------|-----------|
| **旋转轴** | X轴 | Y轴 | Z轴 | 任意轴 |

## 🔧 技术改进点

### 1. 正确的ADR地址映射
- ✅ 修正了`modify_env_param`的地址指向
- ✅ 确保ADR函数能正确修改EventCfg中的参数
- ✅ 实现了渐进式域随机化强度调整

### 2. 符合Isaac Lab架构的设计
- ✅ 完全遵循Isaac Lab官方MDP函数风格
- ✅ 声明式配置，易于理解和修改
- ✅ 模块化设计，可灵活组合

### 3. 人体工程学考虑
- ✅ 添加了基于人手自然姿态的奖励项
- ✅ 鼓励策略学习更自然的抓取姿态
- ✅ 提高了策略的可解释性和安全性

## 🎉 使用示例

### 完整课程学习
```python
from leaphand.tasks.manager_based.leaphand.leaphand_continuous_rot_env_cfg import LeaphandContinuousRotFullCurriculumEnvCfg
env_cfg = LeaphandContinuousRotFullCurriculumEnvCfg()
```

### 仅ADR课程学习
```python
from leaphand.tasks.manager_based.leaphand.leaphand_continuous_rot_env_cfg import LeaphandContinuousRotADROnlyEnvCfg
env_cfg = LeaphandContinuousRotADROnlyEnvCfg()
```

### 自定义课程学习
```python
@configclass
class MyCustomCurriculumCfg:
    # 只启用物体质量ADR，更激进的参数
    object_mass_adr = CurrTerm(
        func=mdp.modify_env_param,
        params={
            "address": "events.object_scale_mass.params.mass_distribution_params",
            "modify_fn": leaphand_mdp.object_mass_adr,
            "modify_params": {
                "enable_step": 400_000,  # 更早启用
                "max_strength_step": 800_000,  # 更快达到最大强度
                "max_variation": 0.7  # 更大的变化范围
            }
        }
    )

env_cfg = LeaphandContinuousRotEnvCfg()
env_cfg.curriculum = MyCustomCurriculumCfg()
```

## ✅ 验证结果

- **语法验证**: 全部通过 ✅
- **关键函数定义**: 全部找到 ✅
- **配置类定义**: 全部正确 ✅
- **地址映射**: 修正完成 ✅
- **奖励项集成**: 成功添加 ✅

系统现在完全符合您的需求，提供了正确的ADR机制、人体工程学奖励项，以及灵活的课程学习配置！🚀
