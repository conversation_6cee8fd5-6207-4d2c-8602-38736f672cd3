---
type: "always_apply"
description: "当每次对话开始时；询问isaacsim/isaaclab等有关问题或基于isaacsim/isaaclab进行开发时"
---

## 核心原则

1.  **🤖 主动确认，持续循环 (Proactive Confirmation, Continuous Loop)**
    *   **操作:** 在执行任何任务时，你必须遵循以下**步进循环**：
        1.  **输出方案:** 在**对话中**输出完整的**下一步**方案（代码、计划、分析等）。
        2.  **请求确认:** 调用 `mcp-feedback-enhanced` 工具，用**一句话摘要**请求我批准。
        3.  **执行:** 获得我的批准后，执行该步骤。
    *   **循环机制:** **一旦步骤执行完毕，重复上述流程，再次调用`mcp-feedback-enhanced`，准备进入下一步。**
    *   **终止条件:** 此循环持续进行，直到我发出明确的**终止指令**（如“任务结束”、“停止对话”）。

2.  **🧐 事实驱动，杜绝猜测**
    *   **信息源优先级:** 按以下顺序获取信息，严禁虚构。
        1.  **本地代码:** IsaacLab 源码、文档、示例。
        2.  **官方文档:** `context7` 工具。
        3.  **网络搜索:** GitHub 仓库等权威技术资料。

## 工作流程

| 流程 | 步骤 |
| :--- | :--- |
| **新功能开发** | 1. **规划:** 在对话中提出实现计划。<br>2. **确认:** 调用 `mcp-feedback-enhanced` 请求批准。<br>3. **执行:** 获得同意后，编码实现。 |
| **错误调试** | 1. **收集:** 在终端中定位关键错误信息。<br>2. **分析:** 在对话中结合日志，提出原因假设和调试步骤。<br>3. **确认:** 调用 `mcp-feedback-enhanced` 请求批准后，开始调试。 |

## 注意

**查询顺序:** 关于isaaclab的问题，优先查询本地IsaacLab源码，其他资料参考 context7 mcp。

## 代码实践

*   **代码隔离:** 绝不修改 IsaacLab 核心代码，开发在独立项目中进行。
*   **风格一致:** 代码和注释风格与 IsaacLab 保持一致。
*   **善用框架:** 优先利用 IsaacLab 的现有功能，避免重复造轮子。